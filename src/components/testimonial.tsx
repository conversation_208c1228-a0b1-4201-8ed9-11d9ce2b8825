import Image from "next/image"
// import { Card } from "@/components/ui/card"
import { Card } from "@ourtrip/ui"
import { Quote } from "lucide-react"

interface TestimonialProps {
  quote: string
  author: string
  role: string
  avatar: string
}

export default function Testimonial({ quote, author, role, avatar }: TestimonialProps) {
  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-md">
      <div className="p-6">
        <Quote className="h-8 w-8 text-primary/40" />
        <p className="mt-4 text-muted-foreground">{quote}</p>
        <div className="mt-6 flex items-center gap-4">
          <div className="relative h-10 w-10 overflow-hidden rounded-full">
            <Image src={avatar || "/placeholder.svg"} alt={author} fill className="object-cover" />
          </div>
          <div>
            <p className="font-medium">{author}</p>
            <p className="text-sm text-muted-foreground">{role}</p>
          </div>
        </div>
      </div>
    </Card>
  )
}
