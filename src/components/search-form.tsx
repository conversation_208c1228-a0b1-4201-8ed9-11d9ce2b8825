"use client"

import { useState } from "react"
import { Calendar } from "lucide-react"

// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"
import { Button } from "@ourtrip/ui"
import { Input } from "@ourtrip/ui"
// import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Popover, PopoverContent, PopoverTrigger } from "@ourtrip/ui"
// import { Calendar as CalendarComponent } from "@/components/ui/calendar"
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ourtrip/ui"

interface SearchFormProps {
  type: "flights" | "hotels" | "packages"
}

export default function SearchForm({ type }: SearchFormProps) {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [returnDate, setReturnDate] = useState<Date | undefined>(new Date(new Date().setDate(new Date().getDate() + 7)))

  return (
    <div className="rounded-xl border bg-card p-6 shadow-sm">
      <form>
        <div className="grid gap-4">
          {type === "flights" && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="departure">From</label>
                <Input id="departure" placeholder="Departure city" />
              </div>
              <div className="space-y-2">
                <label htmlFor="arrival">To</label>
                <Input id="arrival" placeholder="Arrival city" />
              </div>
            </div>
          )}

          {type === "hotels" && (
            <div className="space-y-2">
              <label htmlFor="destination">Destination</label>
              <Input id="destination" placeholder="Where are you going?" />
            </div>
          )}

          {type === "packages" && (
            <div className="grid gap-4">
              <div className="space-y-2">
                <label htmlFor="package-destination">Destination</label>
                <Input id="package-destination" placeholder="Where do you want to go?" />
              </div>
              <div className="space-y-2">
                <label>Package Type</label>
                <Select defaultValue="all-inclusive">
                  <SelectTrigger>
                    <SelectValue placeholder="Select package type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-inclusive">All Inclusive</SelectItem>
                    <SelectItem value="flight-hotel">Flight + Hotel</SelectItem>
                    <SelectItem value="adventure">Adventure Package</SelectItem>
                    <SelectItem value="luxury">Luxury Escape</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label>Departure Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <Calendar className="mr-2 h-4 w-4" />
                    {date ? date.toLocaleDateString() : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  {/* <CalendarComponent mode="single" selected={date} onSelect={setDate} initialFocus /> */}
                </PopoverContent>
              </Popover>
            </div>
            <div className="space-y-2">
              <label>Return Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <Calendar className="mr-2 h-4 w-4" />
                    {returnDate ? returnDate.toLocaleDateString() : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  {/* <CalendarComponent mode="single" selected={returnDate} onSelect={setReturnDate} initialFocus /> */}
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label>Travelers</label>
              <Select defaultValue="2">
                <SelectTrigger>
                  <SelectValue placeholder="Number of travelers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 Traveler</SelectItem>
                  <SelectItem value="2">2 Travelers</SelectItem>
                  <SelectItem value="3">3 Travelers</SelectItem>
                  <SelectItem value="4">4 Travelers</SelectItem>
                  <SelectItem value="5+">5+ Travelers</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {type === "flights" && (
              <div className="space-y-2">
                <label>Class</label>
                <Select defaultValue="economy">
                  <SelectTrigger>
                    <SelectValue placeholder="Select class" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="economy">Economy</SelectItem>
                    <SelectItem value="premium">Premium Economy</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="first">First Class</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
            {type === "hotels" && (
              <div className="space-y-2">
                <label>Rooms</label>
                <Select defaultValue="1">
                  <SelectTrigger>
                    <SelectValue placeholder="Number of rooms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Room</SelectItem>
                    <SelectItem value="2">2 Rooms</SelectItem>
                    <SelectItem value="3">3 Rooms</SelectItem>
                    <SelectItem value="4+">4+ Rooms</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <Button type="submit" className="w-full">
            Search {type.charAt(0).toUpperCase() + type.slice(1)}
          </Button>
        </div>
      </form>
    </div>
  )
}
