// import { Card, CardContent } from "@/components/ui/card"
import { Card } from "@ourtrip/ui"
import type { ReactNode } from "react"

interface TechFeatureProps {
  icon: ReactNode
  title: string
  description: string
}

export default function TechFeature({ icon, title, description }: TechFeatureProps) {
  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-md">
      <div className="p-6">
        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">{icon}</div>
        <h3 className="text-xl font-bold">{title}</h3>
        <p className="mt-2 text-muted-foreground">{description}</p>
      </div>
    </Card>
  )
}
