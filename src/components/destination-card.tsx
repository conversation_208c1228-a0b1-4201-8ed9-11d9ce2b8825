import Image from "next/image"
import { Star } from "lucide-react"

// import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Card } from "@ourtrip/ui"
// import { Badge } from "@/components/ui/badge"
import { Badge } from "@ourtrip/ui"
// import { Button } from "@/components/ui/button"
import { Button } from "@ourtrip/ui"

interface DestinationCardProps {
  name: string
  image: string
  description: string
  price: number
  rating: number
  featured?: boolean
}

export default function DestinationCard({
  name,
  image,
  description,
  price,
  rating,
  featured = false,
}: DestinationCardProps) {
  return (
    <Card
      className={`overflow-hidden transition-all duration-300 hover:shadow-lg ${featured ? "ring-2 ring-primary" : ""}`}
    >
      <div className="relative">
        <Image
          src={image || "/placeholder.svg"}
          alt={name}
          width={600}
          height={400}
          className="aspect-[3/2] w-full object-cover"
        />
        {featured && <Badge type="info" className="absolute right-2 top-2 bg-primary hover:bg-primary/80">Featured</Badge>}
      </div>
      <div className="p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold">{name}</h3>
          <div className="flex items-center">
            <Star className="h-4 w-4 fill-primary text-primary" />
            <span className="ml-1 text-sm font-medium">{rating}</span>
          </div>
        </div>
        <p className="mt-2 text-muted-foreground">{description}</p>
        <div className="mt-4 flex items-center justify-between">
          <div>
            <span className="text-xl font-bold">${price}</span>
            <span className="text-sm text-muted-foreground"> /person</span>
          </div>
          <Badge type="info" className="font-normal">
            AI Recommended
          </Badge>
        </div>
      </div>
      <div className="p-6 pt-0">
        <Button className="w-full">View Details</Button>
      </div>
    </Card>
  )
}
