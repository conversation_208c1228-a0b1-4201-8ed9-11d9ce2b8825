'use client';

import Link from "next/link"
import Image from "next/image"
import { Globe, Compass, Smartphone, HeadsetIcon as VrHeadset, MapPin } from "lucide-react"
import DestinationCard from "@/components/destination-card"
import TechFeature from "@/components/tech-feature"
import Testimonial from "@/components/testimonial"

import { Button } from "@ourtrip/ui"
import { Input } from "@ourtrip/ui"
import { Badge } from "@ourtrip/ui"

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 w-full border border-gray-200 bg-primary-100/95 backdrop-blur supports-[backdrop-filter]:bg-primary-100/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-2">
            <Globe className="h-6 w-6 text-primary-500" />
            <span className="text-xl font-bold">TechTravel</span>
          </div>
          <nav className="hidden md:flex gap-6">
            <Link href="#destinations" className="transition-colors hover:text-primary">
              Destinations
            </Link>
            <Link href="#technology" className="transition-colors hover:text-primary">
              Our Technology
            </Link>
            <Link href="#testimonials" className="transition-colors hover:text-primary">
              Testimonials
            </Link>
            <Link href="#contact" className="transition-colors hover:text-primary">
              Contact
            </Link>
          </nav>
          <div className="flex items-center gap-4">
            <Button color="white" size="small" className="hidden md:flex">
              Sign In
            </Button>
            <Button color="primary" size="small">Get Started</Button>
          </div>
        </div>
      </header>
      <main className="flex-1">
        <div className="relative">
          <div className="container relative z-10 py-24 md:py-32 lg:py-40">
            <div className="flex flex-col items-center space-y-4 text-center text-primary-800">
              <Badge className="bg-primary-900" type="primary" size="small">The Future of Travel</Badge>
              <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
                Travel Smarter with Technology
              </h1>
              <p className="mx-auto max-w-[700px] text-lg text-primary-500/30 md:text-xl">
                Experience destinations like never before with AI-powered trip planning, VR previews, and personalized
                recommendations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                <Button color="primary" size="large">
                  Plan Your Trip
                </Button>
                <Button size="large" variant="outline">
                  Explore VR Experiences
                </Button>
              </div>
            </div>
          </div>
        </div>
        <section id="destinations" className="bg-gray-50 py-12 md:py-16 lg:py-20">
          <div className="container">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Featured Destinations</h2>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Explore our handpicked destinations with immersive virtual previews and AI-curated experiences.
              </p>
            </div>
            <div className="mt-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <DestinationCard
                name="Tokyo, Japan"
                image="/placeholder.svg?height=400&width=600"
                description="Experience the blend of tradition and cutting-edge technology in Japan's capital."
                price={1299}
                rating={4.8}
              />
              <DestinationCard
                name="Santorini, Greece"
                image="/placeholder.svg?height=400&width=600"
                description="Discover the stunning white architecture and breathtaking views of the Aegean Sea."
                price={1099}
                rating={4.9}
                featured={true}
              />
              <DestinationCard
                name="Bali, Indonesia"
                image="/placeholder.svg?height=400&width=600"
                description="Immerse yourself in the lush landscapes and rich cultural heritage of this island paradise."
                price={899}
                rating={4.7}
              />
            </div>
            <div className="mt-10 flex justify-center">
              <Button variant="outline" size="lg">
                View All Destinations
              </Button>
            </div>
          </div>
        </section>

        <section id="technology" className="py-12 md:py-16 lg:py-20">
          <div className="container">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Travel Reimagined</h2>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Our cutting-edge technology transforms how you discover, experience, and remember your journeys.
              </p>
            </div>
            <div className="mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <TechFeature
                icon={<VrHeadset className="h-10 w-10 text-primary" />}
                title="Virtual Reality Previews"
                description="Experience your destination before you arrive with our immersive VR technology. Explore hotels, attractions, and landscapes from the comfort of your home."
              />
              <TechFeature
                icon={<Smartphone className="h-10 w-10 text-primary" />}
                title="AI Travel Assistant"
                description="Our AI companion helps you plan your perfect trip, offering personalized recommendations based on your preferences, budget, and travel history."
              />
              <TechFeature
                icon={<Compass className="h-10 w-10 text-primary" />}
                title="Smart Itineraries"
                description="Automatically generated itineraries that adapt in real-time to weather conditions, local events, and your energy levels throughout your journey."
              />
            </div>
            <div className="mt-16 rounded-2xl bg-gradient-to-r from-primary/20 via-primary/10 to-background p-8 md:p-12">
              <div className="grid gap-8 md:grid-cols-2 items-center">
                <div>
                  <h3 className="text-2xl font-bold md:text-3xl">Experience Our VR Demo</h3>
                  <p className="mt-4 text-muted-foreground">
                    Put on your VR headset and take a virtual walk through the streets of Paris or the beaches of
                    Maldives. No equipment? Use our 360° browser experience instead.
                  </p>
                  <Button className="mt-6">Try VR Demo</Button>
                </div>
                <div className="relative aspect-video overflow-hidden rounded-xl">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    alt="VR Travel Experience"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-12 w-12 rounded-full bg-background/80 backdrop-blur"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-5 w-5"
                      >
                        <polygon points="5 3 19 12 5 21 5 3" />
                      </svg>
                      <span className="sr-only">Play</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="testimonials" className="bg-muted py-12 md:py-16 lg:py-20">
          <div className="container">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">What Our Travelers Say</h2>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Hear from travelers who have experienced the TechTravel difference.
              </p>
            </div>
            <div className="mt-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <Testimonial
                quote="The VR preview of my hotel room saved me from making a huge mistake. I was able to switch to a room with a better view before my trip even started!"
                author="Sarah K."
                role="Business Traveler"
                avatar="/placeholder.svg?height=100&width=100"
              />
              <Testimonial
                quote="The AI travel assistant suggested a local festival that wasn't on any of the tourist maps. It ended up being the highlight of our entire trip!"
                author="Michael T."
                role="Family Vacationer"
                avatar="/placeholder.svg?height=100&width=100"
              />
              <Testimonial
                quote="When our flight was canceled, the smart itinerary automatically rebooked us and adjusted our entire schedule. Saved us hours of stress and phone calls."
                author="Priya M."
                role="Adventure Seeker"
                avatar="/placeholder.svg?height=100&width=100"
              />
            </div>
          </div>
        </section>

        <section className="py-12 md:py-16 lg:py-20">
          <div className="container">
            <div className="rounded-2xl bg-primary text-primary-foreground p-8 md:p-12">
              <div className="grid gap-8 md:grid-cols-2 items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Ready to Travel Smarter?</h2>
                  <p className="mt-4 text-primary-foreground/90 md:text-lg">
                    Join thousands of travelers who have transformed their journeys with our technology-first approach.
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 md:justify-end">
                  <Button size="lg" variant="secondary">
                    Learn More
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
                  >
                    Get Started
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="contact" className="py-12 md:py-16 lg:py-20">
          <div className="container">
            <div className="grid gap-8 md:grid-cols-2">
              <div>
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Contact Us</h2>
                <p className="mt-4 text-muted-foreground md:text-lg">
                  Have questions about our technology or need help planning your next adventure? Our team is here to
                  help.
                </p>
                <div className="mt-8 space-y-4">
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-primary" />
                    <span>123 Innovation Drive, Tech City, TC 10101</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary"
                    >
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                    </svg>
                    <span>+1 (555) 123-4567</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary"
                    >
                      <rect width="20" height="16" x="2" y="4" rx="2" />
                      <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                    </svg>
                    <span><EMAIL></span>
                  </div>
                </div>
              </div>
              <div className="rounded-xl border bg-card p-6 shadow-sm">
                <h3 className="text-xl font-bold">Send Us a Message</h3>
                <form className="mt-6 space-y-4">
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <label htmlFor="name" className="text-sm font-medium">
                        Name
                      </label>
                      <Input id="name" placeholder="Your name" />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium">
                        Email
                      </label>
                      <Input id="email" type="email" placeholder="Your email" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="subject" className="text-sm font-medium">
                      Subject
                    </label>
                    <Input id="subject" placeholder="How can we help?" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="message" className="text-sm font-medium">
                      Message
                    </label>
                    <textarea
                      id="message"
                      className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Tell us about your inquiry..."
                    />
                  </div>
                  <Button type="submit" className="w-full">
                    Send Message
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </section>

        <section className="py-12 md:py-16 lg:py-20 bg-muted">
          <div className="container">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Subscribe to Our Newsletter</h2>
              <p className="mx-auto max-w-[600px] text-muted-foreground">
                Get the latest travel tech updates, exclusive deals, and destination inspiration delivered to your
                inbox.
              </p>
              <div className="mx-auto mt-6 flex w-full max-w-md flex-col gap-2 sm:flex-row">
                <Input placeholder="Enter your email" type="email" className="min-w-0 flex-1" />
                <Button type="submit">Subscribe</Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t bg-background">
        <div className="container flex flex-col gap-8 py-8 md:py-12 lg:py-16">
          <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Globe className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">TechTravel</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Revolutionizing travel experiences through cutting-edge technology and personalized service.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Company</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Press
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Blog
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Support</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Safety Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Community Guidelines
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Legal</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-muted-foreground hover:text-foreground">
                    Cookie Settings
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <p className="text-xs text-muted-foreground">
              © {new Date().getFullYear()} TechTravel. All rights reserved.
            </p>
            <div className="flex gap-4">
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
                <span className="sr-only">Twitter</span>
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
